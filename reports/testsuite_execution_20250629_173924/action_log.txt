Action Log - 2025-06-29 17:39:38
================================================================================

[[17:39:37]] [INFO] Generating execution report...
[[17:39:37]] [WARNING] 2 tests failed.
[[17:39:37]] [INFO] Skipping remaining steps in failed test case (moving from action 9 to next test case at 13)
[[17:39:37]] [INFO] EsFYQdDK3F=fail
[[17:39:37]] [ERROR] Action 9 failed: Failed to launch app: Failed to launch app: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[[17:39:34]] [ERROR] Session restart error: HTTP error! Status: 400
[[17:39:34]] [INFO] Restarting Appium session...
[[17:39:25]] [INFO] EsFYQdDK3F=running
[[17:39:25]] [INFO] Executing action 9/13: Launch app: com.apple.Health
[[17:39:25]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to next test case at 8)
[[17:39:25]] [INFO] To6rgFtm9R=fail
[[17:39:25]] [ERROR] Action 1 failed: Failed to launch app: Failed to launch app: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[[17:39:24]] [INFO] To6rgFtm9R=running
[[17:39:24]] [INFO] Executing action 1/13: Launch app: com.apple.Health
[[17:39:24]] [INFO] ExecutionManager: Starting execution of 13 actions...
[[17:39:24]] [SUCCESS] Cleared 1 screenshots from database
[[17:39:24]] [INFO] Clearing screenshots from database before execution...
[[17:39:24]] [SUCCESS] All screenshots deleted successfully
[[17:39:24]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[17:39:24]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250629_173924/screenshots
[[17:39:24]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250629_173924
[[17:39:24]] [SUCCESS] Report directory initialized successfully
[[17:39:24]] [INFO] Initializing report directory and screenshots folder for test suite...
[[17:39:11]] [SUCCESS] All screenshots deleted successfully
[[17:39:11]] [INFO] All actions cleared
[[17:39:11]] [INFO] Cleaning up screenshots...
[[17:39:07]] [SUCCESS] All screenshots deleted successfully
[[17:39:07]] [INFO] All actions cleared
[[17:39:07]] [INFO] Cleaning up screenshots...
[[17:38:35]] [SUCCESS] All screenshots deleted successfully
[[17:38:35]] [INFO] Cleaning up screenshots...
[[17:38:35]] [SUCCESS] Execution report generated successfully
[[17:38:35]] [SUCCESS] Action logs saved successfully
[[17:38:35]] [ERROR] Execution failed but report was generated.
[[17:38:35]] [INFO] Saving 148 action log entries to file...
[[17:38:35]] [INFO] Generating execution report...
[[17:38:35]] [WARNING] 2 tests failed.
[[17:38:34]] [INFO] Skipping remaining steps in failed test case (moving from action 9 to next test case at 16)
[[17:38:34]] [INFO] To6rgFtm9R=fail
[[17:38:34]] [ERROR] Action 9 failed: Failed to launch app: Failed to launch app: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[[17:38:23]] [INFO] To6rgFtm9R=running
[[17:38:23]] [INFO] Executing action 9/16: Launch app: com.apple.Health
[[17:38:23]] [ERROR] Failed to load test case steps for Multi Step action: HTTP 404: NOT FOUND
[[17:38:23]] [INFO] Loading steps for cleanupSteps action: testing-cleanup
[[17:38:23]] [INFO] cEZOsTdDcx=running
[[17:38:23]] [INFO] Executing action 8/16: cleanupSteps action
[[17:38:23]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to 7), but preserving cleanup steps
[[17:38:23]] [INFO] ee5KkVz90e=fail
[[17:38:23]] [ERROR] Action 1 failed: Failed to terminate app: Failed to terminate app: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[[17:38:21]] [INFO] ee5KkVz90e=running
[[17:38:21]] [INFO] Executing action 1/16: Restart app: com.apple.Health
[[17:38:21]] [INFO] ExecutionManager: Starting execution of 16 actions...
[[17:38:21]] [SUCCESS] Cleared 1 screenshots from database
[[17:38:21]] [INFO] Clearing screenshots from database before execution...
[[17:38:21]] [SUCCESS] All screenshots deleted successfully
[[17:38:21]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[17:38:21]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250629_173821/screenshots
[[17:38:21]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250629_173821
[[17:38:21]] [SUCCESS] Report directory initialized successfully
[[17:38:21]] [INFO] Initializing report directory and screenshots folder for test suite...
[[17:38:14]] [SUCCESS] All screenshots deleted successfully
[[17:38:14]] [INFO] All actions cleared
[[17:38:14]] [INFO] Cleaning up screenshots...
[[17:37:35]] [SUCCESS] Test case health2 saved successfully
[[17:37:35]] [INFO] Saving test case "health2"...
[[17:37:34]] [INFO] Action removed at index 4
[[17:37:28]] [INFO] Action removed at index 0
[[17:37:21]] [SUCCESS] Added terminateApp action
[[17:37:21]] [SUCCESS] Added action: terminateApp
[[17:37:13]] [SUCCESS] Added launchApp action
[[17:37:13]] [SUCCESS] Added action: launchApp
[[17:36:11]] [INFO] Action removed at index 5
[[17:36:08]] [INFO] Action removed at index 3
[[17:36:03]] [INFO] Action removed at index 2
[[17:36:00]] [INFO] Action removed at index 1
[[17:35:55]] [SUCCESS] All screenshots deleted successfully
[[17:35:55]] [SUCCESS] Loaded test case "health2" with 9 actions
[[17:35:55]] [SUCCESS] Added action: addLog
[[17:35:55]] [SUCCESS] Added action: terminateApp
[[17:35:55]] [SUCCESS] Added action: wait
[[17:35:55]] [SUCCESS] Added action: addLog
[[17:35:55]] [SUCCESS] Added action: clickElement
[[17:35:55]] [SUCCESS] Added action: addLog
[[17:35:55]] [SUCCESS] Added action: clickElement
[[17:35:55]] [SUCCESS] Added action: addLog
[[17:35:55]] [SUCCESS] Added action: launchApp
[[17:35:55]] [INFO] All actions cleared
[[17:35:55]] [INFO] Cleaning up screenshots...
[[17:35:47]] [SUCCESS] Test case apple health saved successfully
[[17:35:47]] [INFO] Saving test case "apple health"...
[[17:35:46]] [INFO] Action removed at index 8
[[17:35:41]] [INFO] Action removed at index 5
[[17:35:32]] [SUCCESS] All screenshots deleted successfully
[[17:35:32]] [SUCCESS] Loaded test case "apple health" with 10 actions
[[17:35:32]] [SUCCESS] Added action: addLog
[[17:35:32]] [SUCCESS] Added action: takeScreenshot
[[17:35:32]] [SUCCESS] Added action: terminateApp
[[17:35:32]] [SUCCESS] Added action: clickElement
[[17:35:32]] [SUCCESS] Added action: addLog
[[17:35:32]] [SUCCESS] Added action: clickElement
[[17:35:32]] [SUCCESS] Added action: clickElement
[[17:35:32]] [SUCCESS] Added action: takeScreenshot
[[17:35:32]] [SUCCESS] Added action: clickElement
[[17:35:32]] [SUCCESS] Added action: launchApp
[[17:35:32]] [INFO] All actions cleared
[[17:35:32]] [INFO] Cleaning up screenshots...
[[17:34:38]] [INFO] Action removed
[[17:33:44]] [SUCCESS] Screenshot refreshed successfully
[[17:33:44]] [SUCCESS] Screenshot refreshed successfully
[[17:33:44]] [INFO] Skipping report generation - only single test case executed
[[17:33:44]] [SUCCESS] Action logs saved successfully
[[17:33:44]] [ERROR] Execution failed but report was generated.
[[17:33:44]] [INFO] Saving 70 action log entries to file...
[[17:33:44]] [INFO] Generating execution report...
[[17:33:44]] [WARNING] 1 test failed.
[[17:33:44]] [ERROR] Failed to load test case steps for Multi Step action: HTTP 404: NOT FOUND
[[17:33:44]] [INFO] Loading steps for cleanupSteps action: testing-cleanup
[[17:33:44]] [INFO] cEZOsTdDcx=running
[[17:33:44]] [INFO] Executing action 8/8: cleanupSteps action
[[17:33:44]] [SUCCESS] Screenshot refreshed
[[17:33:44]] [INFO] Refreshing screenshot...
[[17:33:44]] [INFO] RekH7t5GQF=pass
[[17:33:42]] [SUCCESS] Screenshot refreshed successfully
[[17:33:42]] [SUCCESS] Screenshot refreshed successfully
[[17:33:42]] [INFO] RekH7t5GQF=running
[[17:33:42]] [INFO] Executing action 7/8: Launch app: com.apple.Health
[[17:33:41]] [SUCCESS] Screenshot refreshed
[[17:33:41]] [INFO] Refreshing screenshot...
[[17:33:41]] [INFO] mP1IGA6a3b=pass
[[17:33:39]] [SUCCESS] Screenshot refreshed successfully
[[17:33:39]] [SUCCESS] Screenshot refreshed successfully
[[17:33:39]] [INFO] mP1IGA6a3b=running
[[17:33:39]] [INFO] Executing action 6/8: If exists: xpath="//XCUIElementTypeStaticText[@name="Edit"]" (timeout: 10s) → Then perform action: multiStep
[[17:33:39]] [SUCCESS] Screenshot refreshed
[[17:33:39]] [INFO] Refreshing screenshot...
[[17:33:39]] [INFO] 4kBvNvFi5i=pass
[[17:33:36]] [SUCCESS] Screenshot refreshed successfully
[[17:33:36]] [SUCCESS] Screenshot refreshed successfully
[[17:33:36]] [INFO] 4kBvNvFi5i=running
[[17:33:36]] [INFO] Executing action 5/8: Wait for 1 ms
[[17:33:36]] [SUCCESS] Screenshot refreshed
[[17:33:36]] [INFO] Refreshing screenshot...
[[17:33:36]] [INFO] KfOSdvcOkk=pass
[[17:33:32]] [INFO] KfOSdvcOkk=running
[[17:33:32]] [INFO] Executing action 4/8: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:33:32]] [SUCCESS] Screenshot refreshed successfully
[[17:33:32]] [SUCCESS] Screenshot refreshed successfully
[[17:33:32]] [SUCCESS] Screenshot refreshed
[[17:33:32]] [INFO] Refreshing screenshot...
[[17:33:32]] [INFO] E5An5BbVuK=pass
[[17:33:29]] [SUCCESS] Screenshot refreshed successfully
[[17:33:29]] [SUCCESS] Screenshot refreshed successfully
[[17:33:29]] [INFO] E5An5BbVuK=running
[[17:33:29]] [INFO] Executing action 3/8: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:33:29]] [SUCCESS] Screenshot refreshed
[[17:33:29]] [INFO] Refreshing screenshot...
[[17:33:29]] [INFO] JufIPH0oza=pass
[[17:33:27]] [SUCCESS] Screenshot refreshed successfully
[[17:33:27]] [SUCCESS] Screenshot refreshed successfully
[[17:33:27]] [INFO] JufIPH0oza=running
[[17:33:27]] [INFO] Executing action 2/8: info action
[[17:33:27]] [SUCCESS] Screenshot refreshed
[[17:33:27]] [INFO] Refreshing screenshot...
[[17:33:27]] [INFO] ee5KkVz90e=pass
[[17:33:22]] [INFO] ee5KkVz90e=running
[[17:33:22]] [INFO] Executing action 1/8: Restart app: com.apple.Health
[[17:33:22]] [INFO] ExecutionManager: Starting execution of 8 actions...
[[17:33:22]] [SUCCESS] Cleared 1 screenshots from database
[[17:33:22]] [INFO] Clearing screenshots from database before execution...
[[17:33:22]] [SUCCESS] All screenshots deleted successfully
[[17:33:22]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[17:33:22]] [INFO] Skipping report initialization - single test case execution
[[17:33:18]] [SUCCESS] All screenshots deleted successfully
[[17:33:18]] [INFO] All actions cleared
[[17:33:18]] [INFO] Cleaning up screenshots...
[[17:32:55]] [SUCCESS] Screenshot refreshed successfully
[[17:32:54]] [SUCCESS] Screenshot refreshed
[[17:32:54]] [INFO] Refreshing screenshot...
[[17:32:53]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[17:32:53]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[17:32:51]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[17:32:49]] [SUCCESS] Found 1 device(s)
[[17:32:48]] [INFO] Refreshing device list...
