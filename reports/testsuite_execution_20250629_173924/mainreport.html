<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/29/2025, 5:39:38 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">0</span> passed,
                <span class="failed-count">2</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 29/06/2025, 17:39:37
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="8 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #1 apple health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            8 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="failed"
                            data-screenshot="To6rgFtm9R.png" data-action-id="To6rgFtm9R" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: To6rgFtm9R">To6rgFtm9R</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="Screenshot.png" data-action-id="Screenshot" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Take Screenshot: "after_edit_link_click" <span class="action-id-badge" title="Action ID: Screenshot">Screenshot</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="placeholder.png" data-action-id="placeholder" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: placeholder">placeholder</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="Screenshot.png" data-action-id="Screenshot" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Take Screenshot: "after_closing_health_app" <span class="action-id-badge" title="Action ID: Screenshot">Screenshot</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="5 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #2 health2
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            5 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="failed"
                            data-screenshot="To6rgFtm9R.png" data-action-id="To6rgFtm9R" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: To6rgFtm9R">To6rgFtm9R</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath=//XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="placeholder.png" data-action-id="placeholder" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 1000 ms <span class="action-id-badge" title="Action ID: placeholder">placeholder</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="EsFYQdDK3F.png" data-action-id="EsFYQdDK3F" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: EsFYQdDK3F">EsFYQdDK3F</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 29/06/2025, 17:39:37","testCases":[{"name":"apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            8 actions","status":"failed","steps":[{"name":"Launch app: com.apple.Health","status":"failed","duration":"0ms","action_id":"To6rgFtm9R","screenshot_filename":"To6rgFtm9R.png","report_screenshot":"To6rgFtm9R.png","resolved_screenshot":"screenshots/To6rgFtm9R.png","clean_action_id":"To6rgFtm9R","prefixed_action_id":"al_To6rgFtm9R","action_id_screenshot":"screenshots/To6rgFtm9R.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Take Screenshot: \"after_edit_link_click\"","status":"unknown","duration":"0ms","action_id":"Screenshot","screenshot_filename":"Screenshot.png","report_screenshot":"Screenshot.png","resolved_screenshot":"screenshots/Screenshot.png","action_id_screenshot":"screenshots/Screenshot.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: com.apple.Health","status":"unknown","duration":"0ms","action_id":"placeholder","screenshot_filename":"placeholder.png","report_screenshot":"placeholder.png","resolved_screenshot":"screenshots/placeholder.png","clean_action_id":"placeholder","prefixed_action_id":"al_placeholder","action_id_screenshot":"screenshots/placeholder.png"},{"name":"Take Screenshot: \"after_closing_health_app\"","status":"unknown","duration":"0ms","action_id":"Screenshot","screenshot_filename":"Screenshot.png","report_screenshot":"Screenshot.png","resolved_screenshot":"screenshots/Screenshot.png","action_id_screenshot":"screenshots/Screenshot.png"}]},{"name":"health2\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            5 actions","status":"failed","steps":[{"name":"Launch app: com.apple.Health","status":"failed","duration":"0ms","action_id":"To6rgFtm9R","screenshot_filename":"To6rgFtm9R.png","report_screenshot":"To6rgFtm9R.png","resolved_screenshot":"screenshots/To6rgFtm9R.png","clean_action_id":"To6rgFtm9R","prefixed_action_id":"al_To6rgFtm9R","action_id_screenshot":"screenshots/To6rgFtm9R.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath=//XCUIElementTypeButton[@name=\"Done\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 1000 ms","status":"unknown","duration":"0ms","action_id":"placeholder","screenshot_filename":"placeholder.png","report_screenshot":"placeholder.png","resolved_screenshot":"screenshots/placeholder.png","clean_action_id":"placeholder","prefixed_action_id":"al_placeholder","action_id_screenshot":"screenshots/placeholder.png"},{"name":"Terminate app: com.apple.Health","status":"unknown","duration":"0ms","action_id":"EsFYQdDK3F","screenshot_filename":"EsFYQdDK3F.png","report_screenshot":"EsFYQdDK3F.png","resolved_screenshot":"screenshots/EsFYQdDK3F.png","clean_action_id":"EsFYQdDK3F","prefixed_action_id":"al_EsFYQdDK3F","action_id_screenshot":"screenshots/EsFYQdDK3F.png"}]}],"passed":0,"failed":2,"skipped":0,"status":"failed","availableScreenshots":["EsFYQdDK3F.png","To6rgFtm9R.png","latest.png","placeholder.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>