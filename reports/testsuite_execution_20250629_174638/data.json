{"name": "UI Execution 29/06/2025, 17:46:52", "testCases": [{"name": "apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            8 actions", "status": "failed", "steps": [{"name": "Launch app: com.apple.Health", "status": "failed", "duration": "0ms", "action_id": "To6rgFtm9R", "screenshot_filename": "To6rgFtm9R.png", "report_screenshot": "To6rgFtm9R.png", "resolved_screenshot": "screenshots/To6rgFtm9R.png", "clean_action_id": "To6rgFtm9R", "prefixed_action_id": "al_To6rgFtm9R", "action_id_screenshot": "screenshots/To6rgFtm9R.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Take Screenshot: \"after_edit_link_click\"", "status": "unknown", "duration": "0ms", "action_id": "Screenshot", "screenshot_filename": "Screenshot.png", "report_screenshot": "Screenshot.png", "resolved_screenshot": "screenshots/Screenshot.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: com.apple.Health", "status": "unknown", "duration": "0ms", "action_id": "placeholder", "screenshot_filename": "placeholder.png", "report_screenshot": "placeholder.png", "resolved_screenshot": "screenshots/placeholder.png", "clean_action_id": "placeholder", "prefixed_action_id": "al_placeholder", "action_id_screenshot": "screenshots/placeholder.png"}, {"name": "Take Screenshot: \"after_closing_health_app\"", "status": "unknown", "duration": "0ms", "action_id": "Screenshot", "screenshot_filename": "Screenshot.png", "report_screenshot": "Screenshot.png", "resolved_screenshot": "screenshots/Screenshot.png"}]}, {"name": "health2\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            5 actions", "status": "failed", "steps": [{"name": "Launch app: com.apple.Health", "status": "failed", "duration": "0ms", "action_id": "To6rgFtm9R", "screenshot_filename": "To6rgFtm9R.png", "report_screenshot": "To6rgFtm9R.png", "resolved_screenshot": "screenshots/To6rgFtm9R.png", "clean_action_id": "To6rgFtm9R", "prefixed_action_id": "al_To6rgFtm9R", "action_id_screenshot": "screenshots/To6rgFtm9R.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath=//XCUIElementTypeButton[@name=\"Done\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "unknown", "duration": "0ms", "action_id": "placeholder", "screenshot_filename": "placeholder.png", "report_screenshot": "placeholder.png", "resolved_screenshot": "screenshots/placeholder.png", "clean_action_id": "placeholder", "prefixed_action_id": "al_placeholder", "action_id_screenshot": "screenshots/placeholder.png"}, {"name": "Terminate app: com.apple.Health", "status": "unknown", "duration": "0ms", "action_id": "EsFYQdDK3F", "screenshot_filename": "EsFYQdDK3F.png", "report_screenshot": "EsFYQdDK3F.png", "resolved_screenshot": "screenshots/EsFYQdDK3F.png", "clean_action_id": "EsFYQdDK3F", "prefixed_action_id": "al_EsFYQdDK3F", "action_id_screenshot": "screenshots/EsFYQdDK3F.png"}]}], "passed": 0, "failed": 2, "skipped": 0, "status": "failed"}