Action Log - 2025-06-29 17:46:52
================================================================================

[[17:46:52]] [INFO] Generating execution report...
[[17:46:52]] [WARNING] 2 tests failed.
[[17:46:52]] [INFO] Skipping remaining steps in failed test case (moving from action 9 to next test case at 13)
[[17:46:52]] [INFO] EsFYQdDK3F=fail
[[17:46:52]] [ERROR] Action 9 failed: Failed to launch app: Failed to launch app: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[[17:46:47]] [ERROR] Session restart error: HTTP error! Status: 400
[[17:46:47]] [INFO] Restarting Appium session...
[[17:46:40]] [INFO] EsFYQdDK3F=running
[[17:46:40]] [INFO] Executing action 9/13: Launch app: com.apple.Health
[[17:46:40]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to next test case at 8)
[[17:46:40]] [INFO] To6rgFtm9R=fail
[[17:46:40]] [ERROR] Action 1 failed: Failed to launch app: Failed to launch app: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[[17:46:38]] [INFO] To6rgFtm9R=running
[[17:46:38]] [INFO] Executing action 1/13: Launch app: com.apple.Health
[[17:46:38]] [INFO] ExecutionManager: Starting execution of 13 actions...
[[17:46:38]] [SUCCESS] Cleared 3 screenshots from database
[[17:46:38]] [INFO] Clearing screenshots from database before execution...
[[17:46:38]] [SUCCESS] All screenshots deleted successfully
[[17:46:38]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[17:46:38]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250629_174638/screenshots
[[17:46:38]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250629_174638
[[17:46:38]] [SUCCESS] Report directory initialized successfully
[[17:46:38]] [INFO] Initializing report directory and screenshots folder for test suite...
[[17:46:36]] [SUCCESS] All screenshots deleted successfully
[[17:46:36]] [INFO] All actions cleared
[[17:46:36]] [INFO] Cleaning up screenshots...
[[17:42:12]] [SUCCESS] Screenshot refreshed successfully
[[17:42:11]] [SUCCESS] Screenshot refreshed
[[17:42:11]] [INFO] Refreshing screenshot...
[[17:42:10]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[17:42:10]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[17:42:08]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[17:42:04]] [SUCCESS] Test case health2 saved successfully
[[17:42:04]] [INFO] Saving test case "health2"...
[[17:42:03]] [INFO] Action removed at index 4
[[17:41:55]] [SUCCESS] Added wait action
[[17:41:55]] [SUCCESS] Added action: wait
[[17:41:41]] [SUCCESS] All screenshots deleted successfully
[[17:41:41]] [SUCCESS] Loaded test case "health2" with 5 actions
[[17:41:41]] [SUCCESS] Added action: terminateApp
[[17:41:41]] [SUCCESS] Added action: wait
[[17:41:41]] [SUCCESS] Added action: clickElement
[[17:41:41]] [SUCCESS] Added action: clickElement
[[17:41:41]] [SUCCESS] Added action: launchApp
[[17:41:41]] [INFO] All actions cleared
[[17:41:41]] [INFO] Cleaning up screenshots...
[[17:41:41]] [INFO] Selected device: iPhone15,2 (00008120-00186C801E13C01E)
[[17:41:38]] [SUCCESS] Found 1 device(s)
[[17:41:37]] [INFO] Refreshing device list...
