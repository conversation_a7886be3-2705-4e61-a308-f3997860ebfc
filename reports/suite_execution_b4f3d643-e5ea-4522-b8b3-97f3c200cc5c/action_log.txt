Action Log - 2025-06-29 17:33:44
================================================================================

[[17:33:44]] [INFO] Generating execution report...
[[17:33:44]] [WARNING] 1 test failed.
[[17:33:44]] [ERROR] Failed to load test case steps for Multi Step action: HTTP 404: NOT FOUND
[[17:33:44]] [INFO] Loading steps for cleanupSteps action: testing-cleanup
[[17:33:44]] [INFO] cEZOsTdDcx=running
[[17:33:44]] [INFO] Executing action 8/8: cleanupSteps action
[[17:33:44]] [SUCCESS] Screenshot refreshed
[[17:33:44]] [INFO] Refreshing screenshot...
[[17:33:44]] [INFO] RekH7t5GQF=pass
[[17:33:42]] [SUCCESS] Screenshot refreshed successfully
[[17:33:42]] [SUCCESS] Screenshot refreshed successfully
[[17:33:42]] [INFO] RekH7t5GQF=running
[[17:33:42]] [INFO] Executing action 7/8: Launch app: com.apple.Health
[[17:33:41]] [SUCCESS] Screenshot refreshed
[[17:33:41]] [INFO] Refreshing screenshot...
[[17:33:41]] [INFO] mP1IGA6a3b=pass
[[17:33:39]] [SUCCESS] Screenshot refreshed successfully
[[17:33:39]] [SUCCESS] Screenshot refreshed successfully
[[17:33:39]] [INFO] mP1IGA6a3b=running
[[17:33:39]] [INFO] Executing action 6/8: If exists: xpath="//XCUIElementTypeStaticText[@name="Edit"]" (timeout: 10s) → Then perform action: multiStep
[[17:33:39]] [SUCCESS] Screenshot refreshed
[[17:33:39]] [INFO] Refreshing screenshot...
[[17:33:39]] [INFO] 4kBvNvFi5i=pass
[[17:33:36]] [SUCCESS] Screenshot refreshed successfully
[[17:33:36]] [SUCCESS] Screenshot refreshed successfully
[[17:33:36]] [INFO] 4kBvNvFi5i=running
[[17:33:36]] [INFO] Executing action 5/8: Wait for 1 ms
[[17:33:36]] [SUCCESS] Screenshot refreshed
[[17:33:36]] [INFO] Refreshing screenshot...
[[17:33:36]] [INFO] KfOSdvcOkk=pass
[[17:33:32]] [INFO] KfOSdvcOkk=running
[[17:33:32]] [INFO] Executing action 4/8: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:33:32]] [SUCCESS] Screenshot refreshed successfully
[[17:33:32]] [SUCCESS] Screenshot refreshed successfully
[[17:33:32]] [SUCCESS] Screenshot refreshed
[[17:33:32]] [INFO] Refreshing screenshot...
[[17:33:32]] [INFO] E5An5BbVuK=pass
[[17:33:29]] [SUCCESS] Screenshot refreshed successfully
[[17:33:29]] [SUCCESS] Screenshot refreshed successfully
[[17:33:29]] [INFO] E5An5BbVuK=running
[[17:33:29]] [INFO] Executing action 3/8: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:33:29]] [SUCCESS] Screenshot refreshed
[[17:33:29]] [INFO] Refreshing screenshot...
[[17:33:29]] [INFO] JufIPH0oza=pass
[[17:33:27]] [SUCCESS] Screenshot refreshed successfully
[[17:33:27]] [SUCCESS] Screenshot refreshed successfully
[[17:33:27]] [INFO] JufIPH0oza=running
[[17:33:27]] [INFO] Executing action 2/8: info action
[[17:33:27]] [SUCCESS] Screenshot refreshed
[[17:33:27]] [INFO] Refreshing screenshot...
[[17:33:27]] [INFO] ee5KkVz90e=pass
[[17:33:22]] [INFO] ee5KkVz90e=running
[[17:33:22]] [INFO] Executing action 1/8: Restart app: com.apple.Health
[[17:33:22]] [INFO] ExecutionManager: Starting execution of 8 actions...
[[17:33:22]] [SUCCESS] Cleared 1 screenshots from database
[[17:33:22]] [INFO] Clearing screenshots from database before execution...
[[17:33:22]] [SUCCESS] All screenshots deleted successfully
[[17:33:22]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[17:33:22]] [INFO] Skipping report initialization - single test case execution
[[17:33:18]] [SUCCESS] All screenshots deleted successfully
[[17:33:18]] [INFO] All actions cleared
[[17:33:18]] [INFO] Cleaning up screenshots...
[[17:32:55]] [SUCCESS] Screenshot refreshed successfully
[[17:32:54]] [SUCCESS] Screenshot refreshed
[[17:32:54]] [INFO] Refreshing screenshot...
[[17:32:53]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[17:32:53]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[17:32:51]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[17:32:49]] [SUCCESS] Found 1 device(s)
[[17:32:48]] [INFO] Refreshing device list...
