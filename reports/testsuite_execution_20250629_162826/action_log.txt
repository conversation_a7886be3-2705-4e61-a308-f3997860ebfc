Action Log - 2025-06-29 16:29:11
================================================================================

[[16:29:11]] [INFO] Generating execution report...
[[16:29:11]] [WARNING] 1 test failed.
[[16:29:10]] [SUCCESS] Screenshot refreshed
[[16:29:10]] [INFO] Refreshing screenshot...
[[16:29:10]] [INFO] mOoxO3pBlm=pass
[[16:29:07]] [SUCCESS] Screenshot refreshed successfully
[[16:29:07]] [SUCCESS] Screenshot refreshed successfully
[[16:29:07]] [INFO] mOoxO3pBlm=running
[[16:29:07]] [INFO] Executing action 12/12: Terminate app: com.apple.Health
[[16:29:06]] [SUCCESS] Screenshot refreshed
[[16:29:06]] [INFO] Refreshing screenshot...
[[16:29:06]] [INFO] AoLct5ZYWj=pass
[[16:29:03]] [INFO] AoLct5ZYWj=running
[[16:29:03]] [INFO] Executing action 11/12: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[16:29:03]] [SUCCESS] Screenshot refreshed successfully
[[16:29:03]] [SUCCESS] Screenshot refreshed successfully
[[16:29:03]] [SUCCESS] Screenshot refreshed
[[16:29:03]] [INFO] Refreshing screenshot...
[[16:29:03]] [INFO] HphRLWPfSD=pass
[[16:29:00]] [SUCCESS] Screenshot refreshed successfully
[[16:29:00]] [SUCCESS] Screenshot refreshed successfully
[[16:29:00]] [INFO] HphRLWPfSD=running
[[16:29:00]] [INFO] Executing action 10/12: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[16:29:00]] [SUCCESS] Screenshot refreshed
[[16:29:00]] [INFO] Refreshing screenshot...
[[16:29:00]] [INFO] wp1dY1wJ58=pass
[[16:28:48]] [INFO] wp1dY1wJ58=running
[[16:28:48]] [INFO] Executing action 9/12: Launch app: com.apple.Health
[[16:28:48]] [ERROR] Failed to load test case steps for Multi Step action: HTTP 404: NOT FOUND
[[16:28:48]] [INFO] Loading steps for cleanupSteps action: testing-cleanup
[[16:28:48]] [INFO] cEZOsTdDcx=running
[[16:28:48]] [INFO] Executing action 8/12: cleanupSteps action
[[16:28:48]] [INFO] Skipping remaining steps in failed test case (moving from action 4 to 7), but preserving cleanup steps
[[16:28:48]] [INFO] KfOSdvcOkk=fail
[[16:28:48]] [ERROR] Action 4 failed: Element with xpath ' //XCUIElementTypeButton[@name="Done"]' not clickable within timeout of 10 seconds
[[16:28:37]] [SUCCESS] Screenshot refreshed successfully
[[16:28:37]] [SUCCESS] Screenshot refreshed successfully
[[16:28:36]] [INFO] KfOSdvcOkk=running
[[16:28:36]] [INFO] Executing action 4/12: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[16:28:36]] [SUCCESS] Screenshot refreshed
[[16:28:36]] [INFO] Refreshing screenshot...
[[16:28:36]] [INFO] E5An5BbVuK=pass
[[16:28:33]] [SUCCESS] Screenshot refreshed successfully
[[16:28:33]] [SUCCESS] Screenshot refreshed successfully
[[16:28:33]] [INFO] E5An5BbVuK=running
[[16:28:33]] [INFO] Executing action 3/12: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[16:28:33]] [SUCCESS] Screenshot refreshed
[[16:28:33]] [INFO] Refreshing screenshot...
[[16:28:33]] [INFO] JufIPH0oza=pass
[[16:28:31]] [SUCCESS] Screenshot refreshed successfully
[[16:28:31]] [SUCCESS] Screenshot refreshed successfully
[[16:28:31]] [INFO] JufIPH0oza=running
[[16:28:31]] [INFO] Executing action 2/12: info action
[[16:28:31]] [SUCCESS] Screenshot refreshed
[[16:28:31]] [INFO] Refreshing screenshot...
[[16:28:31]] [INFO] ee5KkVz90e=pass
[[16:28:26]] [INFO] ee5KkVz90e=running
[[16:28:26]] [INFO] Executing action 1/12: Restart app: com.apple.Health
[[16:28:26]] [INFO] ExecutionManager: Starting execution of 12 actions...
[[16:28:26]] [SUCCESS] Cleared 1 screenshots from database
[[16:28:26]] [INFO] Clearing screenshots from database before execution...
[[16:28:26]] [SUCCESS] All screenshots deleted successfully
[[16:28:26]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[16:28:26]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250629_162826/screenshots
[[16:28:26]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250629_162826
[[16:28:26]] [SUCCESS] Report directory initialized successfully
[[16:28:26]] [INFO] Initializing report directory and screenshots folder for test suite...
[[16:28:18]] [SUCCESS] All screenshots deleted successfully
[[16:28:18]] [INFO] All actions cleared
[[16:28:18]] [INFO] Cleaning up screenshots...
[[16:28:10]] [SUCCESS] Screenshot refreshed successfully
[[16:28:09]] [SUCCESS] Screenshot refreshed
[[16:28:09]] [INFO] Refreshing screenshot...
[[16:28:08]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[16:28:08]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[16:28:01]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[16:27:59]] [SUCCESS] Found 1 device(s)
[[16:27:59]] [INFO] Refreshing device list...
