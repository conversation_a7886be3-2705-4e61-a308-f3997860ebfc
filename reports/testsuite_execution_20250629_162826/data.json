{"name": "UI Execution 29/06/2025, 16:29:11", "testCases": [{"name": "testing_health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            8 actions", "status": "failed", "steps": [{"name": "Restart app: com.apple.Health", "status": "passed", "duration": "2446ms", "action_id": "HphRLWPfSD", "screenshot_filename": "HphRLWPfSD.png", "report_screenshot": "HphRLWPfSD.png", "resolved_screenshot": "screenshots/HphRLWPfSD.png", "clean_action_id": "HphRLWPfSD", "prefixed_action_id": "al_HphRLWPfSD", "action_id_screenshot": "screenshots/HphRLWPfSD.png"}, {"name": "info action", "status": "passed", "duration": "26ms", "action_id": "AoLct5ZYWj", "screenshot_filename": "AoLct5ZYWj.png", "report_screenshot": "AoLct5ZYWj.png", "resolved_screenshot": "screenshots/AoLct5ZYWj.png", "clean_action_id": "AoLct5ZYWj", "prefixed_action_id": "al_AoLct5ZYWj", "action_id_screenshot": "screenshots/AoLct5ZYWj.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1258ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "failed", "duration": "1644ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 1 ms", "status": "unknown", "duration": "1013ms", "action_id": "JufIPH0oza", "screenshot_filename": "JufIPH0oza.png", "report_screenshot": "JufIPH0oza.png", "resolved_screenshot": "screenshots/JufIPH0oza.png", "clean_action_id": "JufIPH0oza", "prefixed_action_id": "al_JufIPH0oza", "action_id_screenshot": "screenshots/JufIPH0oza.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeStaticText[@name=\"Edit\"]\" (timeout: 10s) → Then perform action: multiStep", "status": "unknown", "duration": "3635ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Launch app: com.apple.Health", "status": "unknown", "duration": "1212ms", "action_id": "placeholder", "screenshot_filename": "placeholder.png", "report_screenshot": "placeholder.png", "resolved_screenshot": "screenshots/placeholder.png", "clean_action_id": "placeholder", "prefixed_action_id": "al_placeholder", "action_id_screenshot": "screenshots/placeholder.png"}, {"name": "cleanupSteps action", "status": "failed", "duration": "3542ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            4 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "61ms", "action_id": "HphRLWPfSD", "screenshot_filename": "HphRLWPfSD.png", "report_screenshot": "HphRLWPfSD.png", "resolved_screenshot": "screenshots/HphRLWPfSD.png", "clean_action_id": "HphRLWPfSD", "prefixed_action_id": "al_HphRLWPfSD", "action_id_screenshot": "screenshots/HphRLWPfSD.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "893ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1605ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1127ms", "action_id": "AoLct5ZYWj", "screenshot_filename": "AoLct5ZYWj.png", "report_screenshot": "AoLct5ZYWj.png", "resolved_screenshot": "screenshots/AoLct5ZYWj.png", "clean_action_id": "AoLct5ZYWj", "prefixed_action_id": "al_AoLct5ZYWj", "action_id_screenshot": "screenshots/AoLct5ZYWj.png"}]}], "passed": 1, "failed": 1, "skipped": 0, "status": "failed"}