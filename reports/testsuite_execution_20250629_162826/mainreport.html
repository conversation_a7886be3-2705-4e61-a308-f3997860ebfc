<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/29/2025, 4:29:11 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">1</span> passed,
                <span class="failed-count">1</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 29/06/2025, 16:29:11
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="8 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #1 testing_health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            8 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="HphRLWPfSD.png" data-action-id="HphRLWPfSD" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: com.apple.Health <span class="action-id-badge" title="Action ID: HphRLWPfSD">HphRLWPfSD</span>
                            </div>
                            <span class="test-step-duration">2446ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="AoLct5ZYWj.png" data-action-id="AoLct5ZYWj" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                info action <span class="action-id-badge" title="Action ID: AoLct5ZYWj">AoLct5ZYWj</span>
                            </div>
                            <span class="test-step-duration">26ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1258ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="failed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1644ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="JufIPH0oza.png" data-action-id="JufIPH0oza" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 1 ms <span class="action-id-badge" title="Action ID: JufIPH0oza">JufIPH0oza</span>
                            </div>
                            <span class="test-step-duration">1013ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                If exists: xpath="//XCUIElementTypeStaticText[@name="Edit"]" (timeout: 10s) → Then perform action: multiStep <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3635ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="placeholder.png" data-action-id="placeholder" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: placeholder">placeholder</span>
                            </div>
                            <span class="test-step-duration">1212ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="failed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">3542ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="4 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #2 apple health (Copy)
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            4 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="HphRLWPfSD.png" data-action-id="HphRLWPfSD" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: HphRLWPfSD">HphRLWPfSD</span>
                            </div>
                            <span class="test-step-duration">61ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">893ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1605ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="AoLct5ZYWj.png" data-action-id="AoLct5ZYWj" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: AoLct5ZYWj">AoLct5ZYWj</span>
                            </div>
                            <span class="test-step-duration">1127ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 29/06/2025, 16:29:11","testCases":[{"name":"testing_health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            8 actions","status":"failed","steps":[{"name":"Restart app: com.apple.Health","status":"passed","duration":"2446ms","action_id":"HphRLWPfSD","screenshot_filename":"HphRLWPfSD.png","report_screenshot":"HphRLWPfSD.png","resolved_screenshot":"screenshots/HphRLWPfSD.png","clean_action_id":"HphRLWPfSD","prefixed_action_id":"al_HphRLWPfSD","action_id_screenshot":"screenshots/HphRLWPfSD.png"},{"name":"info action","status":"passed","duration":"26ms","action_id":"AoLct5ZYWj","screenshot_filename":"AoLct5ZYWj.png","report_screenshot":"AoLct5ZYWj.png","resolved_screenshot":"screenshots/AoLct5ZYWj.png","clean_action_id":"AoLct5ZYWj","prefixed_action_id":"al_AoLct5ZYWj","action_id_screenshot":"screenshots/AoLct5ZYWj.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1258ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"failed","duration":"1644ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 1 ms","status":"unknown","duration":"1013ms","action_id":"JufIPH0oza","screenshot_filename":"JufIPH0oza.png","report_screenshot":"JufIPH0oza.png","resolved_screenshot":"screenshots/JufIPH0oza.png","clean_action_id":"JufIPH0oza","prefixed_action_id":"al_JufIPH0oza","action_id_screenshot":"screenshots/JufIPH0oza.png"},{"name":"If exists: xpath=\"//XCUIElementTypeStaticText[@name=\"Edit\"]\" (timeout: 10s) → Then perform action: multiStep","status":"unknown","duration":"3635ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Launch app: com.apple.Health","status":"unknown","duration":"1212ms","action_id":"placeholder","screenshot_filename":"placeholder.png","report_screenshot":"placeholder.png","resolved_screenshot":"screenshots/placeholder.png","clean_action_id":"placeholder","prefixed_action_id":"al_placeholder","action_id_screenshot":"screenshots/placeholder.png"},{"name":"cleanupSteps action","status":"failed","duration":"3542ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            4 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"61ms","action_id":"HphRLWPfSD","screenshot_filename":"HphRLWPfSD.png","report_screenshot":"HphRLWPfSD.png","resolved_screenshot":"screenshots/HphRLWPfSD.png","clean_action_id":"HphRLWPfSD","prefixed_action_id":"al_HphRLWPfSD","action_id_screenshot":"screenshots/HphRLWPfSD.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"893ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1605ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1127ms","action_id":"AoLct5ZYWj","screenshot_filename":"AoLct5ZYWj.png","report_screenshot":"AoLct5ZYWj.png","resolved_screenshot":"screenshots/AoLct5ZYWj.png","clean_action_id":"AoLct5ZYWj","prefixed_action_id":"al_AoLct5ZYWj","action_id_screenshot":"screenshots/AoLct5ZYWj.png"}]}],"passed":1,"failed":1,"skipped":0,"status":"failed","availableScreenshots":["AoLct5ZYWj.png","E5An5BbVuK.png","HphRLWPfSD.png","JufIPH0oza.png","KfOSdvcOkk.png","ee5KkVz90e.png","latest.png","mOoxO3pBlm.png","placeholder.png","wp1dY1wJ58.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>