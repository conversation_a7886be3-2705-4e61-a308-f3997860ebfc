{"name": "UI Execution 29/06/2025, 17:38:35", "testCases": [{"name": "testing_health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            8 actions", "status": "failed", "steps": [{"name": "Restart app: com.apple.Health", "status": "failed", "duration": "2374ms", "action_id": "To6rgFtm9R", "screenshot_filename": "To6rgFtm9R.png", "report_screenshot": "To6rgFtm9R.png", "resolved_screenshot": "screenshots/To6rgFtm9R.png", "clean_action_id": "To6rgFtm9R", "prefixed_action_id": "al_To6rgFtm9R", "action_id_screenshot": "screenshots/To6rgFtm9R.png"}, {"name": "info action", "status": "unknown", "duration": "0ms", "action_id": "placeholder", "screenshot_filename": "placeholder.png", "report_screenshot": "placeholder.png", "resolved_screenshot": "screenshots/placeholder.png", "clean_action_id": "placeholder", "prefixed_action_id": "al_placeholder", "action_id_screenshot": "screenshots/placeholder.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "unknown", "duration": "1193ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "unknown", "duration": "1644ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 1 ms", "status": "unknown", "duration": "1013ms", "action_id": "ee5KkVz90e", "screenshot_filename": "ee5KkVz90e.png", "report_screenshot": "ee5KkVz90e.png", "resolved_screenshot": "screenshots/ee5KkVz90e.png", "clean_action_id": "ee5KkVz90e", "prefixed_action_id": "al_ee5KkVz90e", "action_id_screenshot": "screenshots/ee5KkVz90e.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeStaticText[@name=\"Edit\"]\" (timeout: 10s) → Then perform action: multiStep", "status": "unknown", "duration": "3635ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Launch app: com.apple.Health", "status": "unknown", "duration": "1212ms", "action_id": "placeholder_report", "screenshot_filename": "placeholder_report.png", "report_screenshot": "placeholder_report.png", "resolved_screenshot": "screenshots/placeholder_report.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report"}, {"name": "cleanupSteps action", "status": "failed", "duration": "3542ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            8 actions", "status": "failed", "steps": [{"name": "Launch app: com.apple.Health", "status": "failed", "duration": "0ms", "action_id": "To6rgFtm9R", "screenshot_filename": "To6rgFtm9R.png", "report_screenshot": "To6rgFtm9R.png", "resolved_screenshot": "screenshots/To6rgFtm9R.png", "clean_action_id": "To6rgFtm9R", "prefixed_action_id": "al_To6rgFtm9R", "action_id_screenshot": "screenshots/To6rgFtm9R.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Take Screenshot: \"after_edit_link_click\"", "status": "unknown", "duration": "0ms", "action_id": "Screenshot", "screenshot_filename": "Screenshot.png", "report_screenshot": "Screenshot.png", "resolved_screenshot": "screenshots/Screenshot.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: com.apple.Health", "status": "unknown", "duration": "0ms", "action_id": "placeholder", "screenshot_filename": "placeholder.png", "report_screenshot": "placeholder.png", "resolved_screenshot": "screenshots/placeholder.png", "clean_action_id": "placeholder", "prefixed_action_id": "al_placeholder", "action_id_screenshot": "screenshots/placeholder.png"}, {"name": "Take Screenshot: \"after_closing_health_app\"", "status": "unknown", "duration": "0ms", "action_id": "Screenshot", "screenshot_filename": "Screenshot.png", "report_screenshot": "Screenshot.png", "resolved_screenshot": "screenshots/Screenshot.png"}]}], "passed": 0, "failed": 2, "skipped": 0, "status": "failed"}