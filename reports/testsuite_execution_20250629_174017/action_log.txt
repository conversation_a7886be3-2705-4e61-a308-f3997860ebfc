Action Log - 2025-06-29 17:41:36
================================================================================

[[17:41:36]] [INFO] Generating execution report...
[[17:41:36]] [WARNING] 1 test failed.
[[17:41:36]] [INFO] Skipping remaining steps in failed test case (moving from action 12 to next test case at 13)
[[17:41:36]] [ERROR] Error executing action 12: Failed to fetch
[[17:41:05]] [SUCCESS] Screenshot refreshed successfully
[[17:41:05]] [SUCCESS] Screenshot refreshed successfully
[[17:41:05]] [INFO] ag29wsBP24=running
[[17:41:05]] [INFO] Executing action 12/13: Wait for 1000 ms
[[17:41:04]] [SUCCESS] Screenshot refreshed
[[17:41:04]] [INFO] Refreshing screenshot...
[[17:41:04]] [INFO] XCUIElemen2=pass
[[17:41:01]] [SUCCESS] Screenshot refreshed successfully
[[17:41:01]] [SUCCESS] Screenshot refreshed successfully
[[17:41:01]] [INFO] XCUIElemen2=running
[[17:41:01]] [INFO] Executing action 11/13: Click element: xpath=//XCUIElementTypeButton[@name="Done"]
[[17:41:01]] [SUCCESS] Screenshot refreshed
[[17:41:01]] [INFO] Refreshing screenshot...
[[17:41:01]] [INFO] XCUIElemen=pass
[[17:40:58]] [SUCCESS] Screenshot refreshed successfully
[[17:40:58]] [SUCCESS] Screenshot refreshed successfully
[[17:40:58]] [INFO] XCUIElemen=running
[[17:40:58]] [INFO] Executing action 10/13: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:40:58]] [SUCCESS] Screenshot refreshed
[[17:40:58]] [INFO] Refreshing screenshot...
[[17:40:58]] [INFO] EsFYQdDK3F=pass
[[17:40:46]] [SUCCESS] Screenshot refreshed successfully
[[17:40:46]] [SUCCESS] Screenshot refreshed successfully
[[17:40:45]] [INFO] EsFYQdDK3F=running
[[17:40:45]] [INFO] Executing action 9/13: Launch app: com.apple.Health
[[17:40:44]] [SUCCESS] Screenshot refreshed
[[17:40:44]] [INFO] Refreshing screenshot...
[[17:40:44]] [INFO] WaakzZc6xF=pass
[[17:40:41]] [SUCCESS] Screenshot refreshed successfully
[[17:40:41]] [SUCCESS] Screenshot refreshed successfully
[[17:40:41]] [INFO] WaakzZc6xF=running
[[17:40:41]] [INFO] Executing action 8/13: Take Screenshot: "after_closing_health_app"
[[17:40:40]] [SUCCESS] Screenshot refreshed
[[17:40:40]] [INFO] Refreshing screenshot...
[[17:40:40]] [INFO] jF4jRny1iE=pass
[[17:40:37]] [SUCCESS] Screenshot refreshed successfully
[[17:40:37]] [SUCCESS] Screenshot refreshed successfully
[[17:40:37]] [INFO] jF4jRny1iE=running
[[17:40:37]] [INFO] Executing action 7/13: Terminate app: com.apple.Health
[[17:40:36]] [SUCCESS] Screenshot refreshed
[[17:40:36]] [INFO] Refreshing screenshot...
[[17:40:36]] [INFO] oIAtyQB5wY=pass
[[17:40:33]] [INFO] oIAtyQB5wY=running
[[17:40:33]] [INFO] Executing action 6/13: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:40:33]] [SUCCESS] Screenshot refreshed successfully
[[17:40:33]] [SUCCESS] Screenshot refreshed successfully
[[17:40:32]] [SUCCESS] Screenshot refreshed
[[17:40:32]] [INFO] Refreshing screenshot...
[[17:40:32]] [INFO] 7MOUNxtPJz=pass
[[17:40:30]] [SUCCESS] Screenshot refreshed successfully
[[17:40:30]] [SUCCESS] Screenshot refreshed successfully
[[17:40:30]] [INFO] 7MOUNxtPJz=running
[[17:40:30]] [INFO] Executing action 5/13: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:40:29]] [SUCCESS] Screenshot refreshed
[[17:40:29]] [INFO] Refreshing screenshot...
[[17:40:29]] [INFO] UppP3ZuqY6=pass
[[17:40:26]] [INFO] UppP3ZuqY6=running
[[17:40:26]] [INFO] Executing action 4/13: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:40:26]] [SUCCESS] Screenshot refreshed successfully
[[17:40:26]] [SUCCESS] Screenshot refreshed successfully
[[17:40:25]] [SUCCESS] Screenshot refreshed
[[17:40:25]] [INFO] Refreshing screenshot...
[[17:40:25]] [INFO] ag29wsBP24=pass
[[17:40:23]] [INFO] ag29wsBP24=running
[[17:40:23]] [INFO] Executing action 3/13: Take Screenshot: "after_edit_link_click"
[[17:40:23]] [SUCCESS] Screenshot refreshed successfully
[[17:40:23]] [SUCCESS] Screenshot refreshed successfully
[[17:40:23]] [SUCCESS] Screenshot refreshed
[[17:40:23]] [INFO] Refreshing screenshot...
[[17:40:23]] [INFO] SaJtvXOGlT=pass
[[17:40:20]] [SUCCESS] Screenshot refreshed successfully
[[17:40:20]] [SUCCESS] Screenshot refreshed successfully
[[17:40:20]] [INFO] SaJtvXOGlT=running
[[17:40:20]] [INFO] Executing action 2/13: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:40:19]] [SUCCESS] Screenshot refreshed
[[17:40:19]] [INFO] Refreshing screenshot...
[[17:40:19]] [INFO] To6rgFtm9R=pass
[[17:40:17]] [INFO] To6rgFtm9R=running
[[17:40:17]] [INFO] Executing action 1/13: Launch app: com.apple.Health
[[17:40:17]] [INFO] ExecutionManager: Starting execution of 13 actions...
[[17:40:17]] [SUCCESS] Cleared 1 screenshots from database
[[17:40:17]] [INFO] Clearing screenshots from database before execution...
[[17:40:17]] [SUCCESS] All screenshots deleted successfully
[[17:40:17]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[17:40:17]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250629_174017/screenshots
[[17:40:17]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250629_174017
[[17:40:17]] [SUCCESS] Report directory initialized successfully
[[17:40:17]] [INFO] Initializing report directory and screenshots folder for test suite...
[[17:40:15]] [SUCCESS] All screenshots deleted successfully
[[17:40:15]] [INFO] All actions cleared
[[17:40:15]] [INFO] Cleaning up screenshots...
[[17:40:12]] [SUCCESS] Screenshot refreshed successfully
[[17:40:12]] [SUCCESS] Screenshot refreshed
[[17:40:12]] [INFO] Refreshing screenshot...
[[17:40:11]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[17:40:11]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[17:40:08]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[17:40:05]] [SUCCESS] Found 1 device(s)
[[17:40:04]] [INFO] Refreshing device list...
